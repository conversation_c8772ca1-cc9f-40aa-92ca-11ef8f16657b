<?php
require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== eBay Authentication Debug ===\n";

try {
    // Get all admin users with their eBay auth info
    $users = DB::table('admin_users')
        ->select('id', 'username', 'access_token', 'access_token_expires_in', 'refresh_token', 'ebay_id', 'ebay_username', 'member_type', 'permission_type')
        ->get();

    foreach ($users as $user) {
        echo "\n--- User ID: {$user->id} ({$user->username}) ---\n";
        echo "Member Type: {$user->member_type}\n";
        echo "Permission Type: {$user->permission_type}\n";
        echo "eBay ID: " . ($user->ebay_id ?: 'NULL') . "\n";
        echo "eBay Username: " . ($user->ebay_username ?: 'NULL') . "\n";
        echo "Access Token: " . ($user->access_token ? 'EXISTS' : 'NULL') . "\n";
        echo "Access Token Expires: " . ($user->access_token_expires_in ?: 'NULL') . "\n";
        echo "Refresh Token: " . ($user->refresh_token ? 'EXISTS' : 'NULL') . "\n";
        
        // Check if auth is valid
        if ($user->access_token_expires_in) {
            $expires = new DateTime($user->access_token_expires_in);
            $now = new DateTime();
            $isValid = $expires > $now;
            echo "Auth Status: " . ($isValid ? 'VALID' : 'EXPIRED') . "\n";
            echo "Expires At: " . $expires->format('Y-m-d H:i:s') . "\n";
            echo "Current Time: " . $now->format('Y-m-d H:i:s') . "\n";
        } else {
            echo "Auth Status: NO TOKEN\n";
        }
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
?>
