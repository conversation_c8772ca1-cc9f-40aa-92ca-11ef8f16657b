<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client as GuzzleClient;
use App\Utilities\HtmlDomUtility;
use App\Models\Exhibit;

use Mail;

class CheckInventoryItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:check-inventory-items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check inventory items';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        Log::setDefaultDriver('batch');
        Log::info('-- Check inventory items --');
        // 連携済みのユーザ一覧取得
        $users = DB::table('admin_users')->whereNotNull('refresh_token')->where('permission_type', 3)->where('member_type', 1)->get();
        foreach ($users as $user) {
            // ユーザに紐づく出品情報を取得
            // 出品済みかつ未削除で在庫がありそうなのを取得
            //$items = Exhibit::where('admin_user_id', $user->id)->whereNull('deleted_at')->whereNotNull('offer_id')->where('status', 2)->where('is_referer_soldout', 0)->get();
            $items = Exhibit::where('admin_user_id', $user->id)->whereNull('deleted_at')->whereNotNull('offer_id')->whereNotNull('item_id')->where('status', 2)->where('is_referer_soldout', 0)->orderBy('id', 'DESC')->get();
            $content = [];
            $is_need_mail = false;
            foreach ($items as $item) {
                $content_row = [];
                Log::info(" ----- Exhibit ID : " . $item->id . " ----- ");

                // 出品元を取得
                // 各種から状況取得、ない場合false
                $ret = [];
                switch ($item->referer_service) {
                    case "yahoo":
                        if (filter_var($item->referer_id, FILTER_VALIDATE_URL)) {
                            $referer = $item->referer_id;
                        } else {
                            $referer = 'https://page.auctions.yahoo.co.jp/jp/auction/' . $item->referer_id;
                        }
                        $ret = self::getYahooItem($referer);
                        $content_row["url"] = $referer;
                        break;
                    case "mercari":
                        if (filter_var($item->referer_id, FILTER_VALIDATE_URL)) {
                            $referer = $item->referer_id;
                        } else {
                            $referer = 'https://jp.mercari.com/item/' . $item->referer_id;
                        }
                        $ret = self::getMercariItem($referer);
                        $content_row["url"] = $referer;
                        break;
                    case "amazon":
                        if (filter_var($item->referer_id, FILTER_VALIDATE_URL)) {
                            $referer = $item->referer_id;
                        } else {
                            $referer = 'https://www.amazon.co.jp/dp/' . $item->referer_id;
                        }
                        $ret = self::getAmazonItem($referer);
                        $content_row["url"] = $referer;
                        break;
                    default;
                        // 指定がない場合trueを返す
                }
                // 情報が取れた
                Log::info('Referer Data:' . json_encode($ret));
                if ($ret) {

                    $content_row['id'] = $item->id;
                    $content_row['title'] = $item->title;
                    $content_row['sku'] = $item->sku;

                    // if($ret["price"] != $item->referer_price || $ret["is_referer_mail"] == false){
                    if (
                        (isset($ret["price"]) && str_replace(',', '', $ret["price"]) != $item->referer_price) ||
                        ($item->is_referer_mail == false)
                    ) {
                        $is_need_mail = true;
                        Log::info(' Referer Price Changed. ');
                        $content_row['message'] = '参照元の価格が変更されたことが確認されました。';
                        Exhibit::where('id', $item->id)->update([
                            'referer_price' => str_replace(',', '', $ret["price"]),
                            'is_referer_mail' => true,
                        ]);

                        $content[] = $content_row;
                    }

                    if ($ret["is_closed"] == true) {
                        $is_need_mail = true;
                        // 売れた判定
                        Log::info(' Referer Soldout. ');
                        // 参照元が消えたステータスに
                        Exhibit::where('id', $item->id)->update([
                            'is_referer_soldout' => 1,
                        ]);

                        // 出品状態を取得
                        $client = null;
                        $client = new GuzzleClient();
                        // ユーザ毎の出品データを取得
                        //GET https://api.ebay.com/sell/inventory/v1/offer/{offerId}
                        $response = $client->request('GET', config('ebay.api_endpoint') . '/sell/inventory/v1/offer/' . $item->offer_id, [
                            'http_errors' => false,
                            'headers' => [
                                'Authorization' => [
                                    'Bearer ' . $user->access_token,
                                ],
                                'Accept' => ['application/json'],
                                'Content-Type' => ['application/json'],
                                'Content-Language' => ['en-US'],
                            ],
                        ]);
                        $content = json_decode($response->getBody()->getContents(), true);
                        Log::info('GET ' . config('ebay.api_endpoint') . '/sell/inventory/v1/offer/' . $item->offer_id);
                        Log::info(" Response \n HTTP Status: " . $response->getStatusCode() . " \n Body :" . $response->getBody()->getContents());


                        // 在庫があったら取り消し処理
                        if (isset($content["availableQuantity"]) && $content["availableQuantity"] != 0) {
                            //// 売れ残ってたらeBay側の出品を取り消す
                            //$client = null;
                            //$client = new GuzzleClient();
                            //// DELETE https://api.ebay.com/sell/inventory/v1/offer/{offerId}
                            //$response = $client->request('DELETE',config('ebay.api_endpoint').'/sell/inventory/v1/offer/'.$item->offer_id,[
                            //	'http_errors' => false,
                            //	'headers' => [
                            //		'Authorization' => [
                            //			'Bearer '.$user->access_token,
                            //		],
                            //		'Accept' => ['application/json'],
                            //		'Content-Type' => ['application/json'],
                            //		'Content-Language' => ['en-US'],
                            //	],
                            //]);
                            //
                            //Log::info('DELETE '.config('ebay.api_endpoint').'/sell/inventory/v1/offer/'.$item->offer_id);
                            //Log::info(" Response \n HTTP Status: ".$response->getStatusCode()." \n Body :".$response->getBody()->getContents());
                            //// eBay側消したフラグに
                            //Exhibit::where('id', $item->id)->update([
                            //	'status' => 4,
                            //]);
                            //$content_row['message'] = '参照元の商品が取得できなかったため、eBay側の出品を取り消しました。';
                            $content_row['message'] = 'eBay側の出品状況が取得できませんでした。';
                        } else if ($response->getStatusCode() == 404) {
                            $content_row['message'] = 'eBay側の出品状況が取得できませんでした。';
                        } else {
                            $content_row['message'] = '参照元の商品が取得できませんでした。また、eBay側の出品状況をご確認ください。';
                        }
                        $content[] = $content_row;
                    }
                }
            }

            if ($is_need_mail) {
                Log::info(json_encode($content));
                self::sendMail(1, $user->username, $user->name, $content);
            }
        }
    }

    private function sendMail($id, $to, $name, $contents)
    {
        if ($id == 1) {
            // 在庫が確認出来なくなった場合
            Mail::send(
                'emails.caution',
                [
                    'name' => $name,
                    'contents' => $contents,
                ],
                function ($m) use ($to, $name) {
                    $m->from('<EMAIL>', 'サポチャPro');
                    $m->to($to, $name)->subject('価格変更のお知らせ');
                }
            );
        } else if ($id == 2) {
            // 巡回メール
            Mail::send(
                'emails.content',
                [
                    'name' => $name,
                    'contents' => $contents,
                ],
                function ($m) use ($to, $name) {
                    $m->from('<EMAIL>', 'サポチャPro');
                    $m->to($to, $name)->subject('サポチャPro巡回メール');
                }
            );
        }
    }

    /**
     * ヤフオクからタイトルを取得
     */
    private function getYahooItem($referer_id)
    {
        //-----------------------------------------------
        //$url = "https://page.auctions.yahoo.co.jp/jp/auction/".$referer_id;
        $url = $referer_id;
        $html = HtmlDomUtility::getHtml($url);
        $dom = HtmlDomUtility::getHtmlToDom($html);
        //-----------------------------------------------
        $is_closed = false;
        $obj_product_closed = $dom->find('.ClosedHeader__tag');
        foreach ($obj_product_closed as $sub_dom) {
            Log::info('sub dom ' . $sub_dom);
            $is_closed = true;
        }
        $product_price = "";
        $obj_product_price_doms = $dom->find('.Price__value');
        foreach ($obj_product_price_doms as $sub_dom) {
            $sub_dom2 = $sub_dom->find('text');
            if (count($sub_dom2) > 0) {
                $product_price = preg_replace('/[^0-9]/', '', trim($sub_dom2[0]->plaintext));
            }
        }

        //-----------------------------------------------
        $dom->clear();

        return ['is_closed' => $is_closed, 'price' => $product_price];
    }

    /**
     * メルカリからタイトルを取得
     */
    private function getMercariItem($referer_id)
    {
        //$url = 'https://jp.mercari.com/item/'.$referer_id;
        $url = $referer_id;
        $output = shell_exec('sudo -u ec2-user /home/<USER>/.nvm/versions/node/v14.17.3/bin/node /srv/app/nodejs/get_mercari_product.js ' . $url);

        $product["output"] = $output;
        $ret = json_decode($output, true);

        $state = isset($ret["state"]) ? $ret["state"] : '';
        $price = isset($ret["price"]) ? $ret["price"] : '';

        return ['is_closed' => ($state) ? false : true, 'price' => $price];
    }

    /**
     * Amazonからタイトルを取得
     */
    private function getAmazonItem($referer_id)
    {
        //$url = "https://www.amazon.co.jp/dp/".$referer_id;
        $url = $referer_id;
        $html = HtmlDomUtility::getHtml($url);
        $dom = HtmlDomUtility::getHtmlToDom($html);
        //-----------------------------------------------
        //商品名
        $product_title = "";
        $obj_product_title_doms = $dom->find('#titleSection');
        foreach ($obj_product_title_doms as $sub_dom) {
            $sub_dom2 = $sub_dom->find('#title');
            foreach ($sub_dom2 as $sub_dom3) {
                $sub_dom4 = $sub_dom3->find('#productTitle');
                foreach ($sub_dom4 as $sub_dom5) {
                    $product_title = $sub_dom5->text();
                }
            }
        }
        //-----------------------------------------------
        //商品詳細
        $product_info = "";
        $obj_product_info_doms = $dom->find('#productDetails_feature_div');
        foreach ($obj_product_info_doms as $sub_dom) {
            $sub_dom2 = $sub_dom->find('#prodDetails');
            foreach ($sub_dom2 as $sub_dom3) {
                $sub_dom4 = $sub_dom3->find('#productDetails_techSpec_section_1');
                foreach ($sub_dom4 as $sub_dom5) {
                    $product_info = $sub_dom5->innertext();
                }
            }
        }
        // 価格
        $product_price = "";
        $obj_product_price_doms = $dom->find('.a-price-whole');
        foreach ($obj_product_price_doms as $sub_dom) {
            $sub_dom2 = $sub_dom->find('text');
            if (count($sub_dom2) > 0) {
                $product_price = preg_replace('/[^0-9]/', '', trim($sub_dom2[0]->plaintext));
            }
        }

        return ['is_closed' => ($product_title) ? false : true, 'price' => $product_price];
    }
}
