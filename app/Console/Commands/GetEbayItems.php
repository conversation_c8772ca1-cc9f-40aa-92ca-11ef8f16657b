<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client as GuzzleClient;
use App\Utilities\HtmlDomUtility;
use App\Models\Exhibit;

use Mail;

class GetEbayItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:get-ebay-items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get eBay Items status';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
		Log::setDefaultDriver('batch');
		Log::info('-- Check eBay Status --');
        // ユーザ一覧取得
		$users = DB::table('admin_users')->whereNotNull('refresh_token')->where('permission_type',3)->where('member_type',1)->get();
		foreach($users as $user){
			$contents = [];
			// ユーザに紐づく出品情報を取得
			$items = Exhibit::where('admin_user_id', $user->id)->whereNull('deleted_at')->whereNotNull('offer_id')->whereNotNull('item_id')->where('status', 2)->where('is_referer_soldout', 0)->orderBy('id','DESC')->get();
			foreach($items as $item){
				$content_row = [];
				Log::info(" ----- Exhibit ID : ".$item->id." ----- ");
				$is_sold = false;
				// 出品状態を取得
				$client = null;
				$client = new GuzzleClient();
				// ユーザ毎の出品データを取得
				//GET https://api.ebay.com/sell/inventory/v1/offer/{offerId}
				$response = $client->request('GET',config('ebay.api_endpoint').'/sell/inventory/v1/offer/'.$item->offer_id,[
                    'http_errors' => false,
                    'headers' => [
                        'Authorization' => [
                            'Bearer '.$user->access_token,
                        ],
                        'Accept' => ['application/json'],
                        'Content-Type' => ['application/json'],
                        'Content-Language' => ['en-US'],
                    ],
                ]);
				
				$content = json_decode($response->getBody()->getContents(),true);
				Log::info('GET '.config('ebay.api_endpoint').'/sell/inventory/v1/offer/'.$item->offer_id);
				Log::info(" Response \n HTTP Status: ".$response->getStatusCode()." \n Body :".$response->getBody()->getContents());
				
				// ItemIDを保存
				$item_id = null;
				if( isset($content['listing']) && isset($content['listing']['listingId'])){
					$item_id = $content['listing']['listingId'];
					Exhibit::where('id',$item->id)->update(['item_id' => $item_id]);
				}
				
				if( isset($content["availableQuantity"]) && $content["availableQuantity"] != 0) {
					// 在庫があったらそのまま 
					$content_row['status'] = '販売中';
				} else if($response->getStatusCode() == 404){
					// eBay側が消されてたら消されてた判定
					Exhibit::where('id', $item->id)->update([
						'status' => 4,
					]);
					$is_sold = false;
					$content_row['status'] = '販売停止済み';
				} else {
					// 売れた
					Exhibit::where('id', $item->id)->update([
						'status' => 3,
					]);
					$is_sold = true;
					$content_row['status'] = '売却済み';
				}
				$content_row['id'] = $item->id;
				$content_row['title'] = $item->title;
				$content_row['sku'] = $item->sku;
				
				// 出品元を取得
				// 各種から状況取得、ない場合false
				$state = false;
				switch($item->referer_service) {
					case "yahoo":
						$state = self::getYahooItem($item->referer_id);
						break;
					case "mercari":
						$state = self::getMercariItem($item->referer_id);
						break;
					case "amazon":
						$state = self::getAmazonItem($item->referer_id);
						break;
					default;
						// 指定がない場合trueを返す
						$state = true;
				}
				// 情報が取れない or 明示的に終わってた場合
				if(!$state) {
					Log::info(' Referer Soldout. ');
					// 参照元が消えたステータスに
					Exhibit::where('id', $item->id)->update([
						'is_referer_soldout' => 1,
					]);
					
					$content_row['status'] = '参照元在庫切れ';
					
					if(!$is_sold){
						// 売れ残ってたらeBay側の出品を取り消す
						$client = null;
						$client = new GuzzleClient();
						// DELETE https://api.ebay.com/sell/inventory/v1/offer/{offerId}
						$response = $client->request('DELETE',config('ebay.api_endpoint').'/sell/inventory/v1/offer/'.$item->offer_id,[
							'http_errors' => false,
							'headers' => [
								'Authorization' => [
									'Bearer '.$user->access_token,
								],
								'Accept' => ['application/json'],
								'Content-Type' => ['application/json'],
								'Content-Language' => ['en-US'],
							],
						]);
						
						Log::info('DELETE '.config('ebay.api_endpoint').'/sell/inventory/v1/offer/'.$item->offer_id);
						Log::info(" Response \n HTTP Status: ".$response->getStatusCode()." \n Body :".$response->getBody()->getContents());
						// eBay側を消すように
						Exhibit::where('id', $item->id)->update([
							'status' => 4,
						]);
						$content_row['status'] = '参照元在庫切れ(eBay取り消し済み)';
					}
				}
				$contents[$content_row['id']] = $content_row;
			}
			
			$date = date('Y-m-d H:i:s', strtotime('-2 day'));
			$items2 = Exhibit::where('admin_user_id', $user->id)->whereNull('deleted_at')->whereNotNull('offer_id')->whereNotNull('item_id')->where('status', 4)->where('is_referer_soldout', 1)->where('updated_at','>',$date)->get();
			foreach($items2 as $item){
				$content_row = [];
				$content_row['id'] = $item->id;
				$content_row['title'] = $item->title;
				$content_row['sku'] = $item->sku;
				$content_row['status'] = '参照元在庫切れ(eBay取り消し済み)';
				$contents[$content_row['id']] = $content_row;
			}
			
			self::sendMail(2,$user->username, $user->name, $contents);
		}
    }
	
	private function sendMail($id,$to,$name,$contents){
		if($id == 1){
			// 在庫が確認出来なくなった場合
			Mail::send(
				'emails.caution',
				[
					'name' => $name,
					'contents' => $contents,
				],
				function($m) use ($to,$name) {
					$m->from('<EMAIL>','サポチャPro');
					$m->to($to,$name)->subject('価格変更のお知らせ');
				}
			);
		} else if($id == 2){
			// 巡回メール
			Mail::send(
				'emails.content',
				[
					'name' => $name,
					'contents' => $contents,
				],
				function($m) use ($to,$name) {
					$m->from('<EMAIL>','サポチャPro');
					$m->to($to,$name)->subject('サポチャPro巡回メール');
				}
			);
		}
		
	}
	
	/**
	 * ヤフオクからタイトルを取得
	*/
	private function getYahooItem($url){
		if(preg_match('/https?:\/{2}[\w\/:%#\$&\?\(\)~\.=\+\-]+/',$url)){
			//
		} else {
			$url = "https://page.auctions.yahoo.co.jp/jp/auction/".$url;
		}
		//-----------------------------------------------
		
		$html = HtmlDomUtility::getHtml($url);
		$dom = HtmlDomUtility::getHtmlToDom($html);
		//-----------------------------------------------
		$is_closed = false;
		$obj_product_closed = $dom->find('.ClosedHeader__tag');
		foreach ($obj_product_closed as $sub_dom) {
			Log::info('sub dom '.$sub_dom);
			$is_closed = true;
		}
		
		//-----------------------------------------------
		$dom->clear();
		
		return !$is_closed;
	}
	
	/**
	 * メルカリからタイトルを取得
	*/
	private function getMercariItem($url){
		if(preg_match('/https?:\/{2}[\w\/:%#\$&\?\(\)~\.=\+\-]+/',$url)){
			//
		} else {
			$url = 'https://jp.mercari.com/item/'.$url;
		}
		
		$output = shell_exec('sudo -u ec2-user /home/<USER>/.nvm/versions/node/v14.17.3/bin/node /srv/app/nodejs/get_mercari_product.js '.$url);
		
		$product["output"] = $output;
		$ret = json_decode($output,true);
		
		$state = isset($ret["state"]) ? $ret["state"] : '';
		
		return ($state == 1) ? true : false;
	}
	
	/**
	 * Amazonからタイトルを取得
	*/
	private function getAmazonItem($url){
		if(preg_match('/https?:\/{2}[\w\/:%#\$&\?\(\)~\.=\+\-]+/',$url)){
			//
		} else {
			$url = "https://www.amazon.co.jp/dp/".$url;
		}
		$output = shell_exec('sudo -u ec2-user /home/<USER>/.nvm/versions/node/v14.17.3/bin/node /srv/app/nodejs/get_amazon_product.js '.$url);
		$product["output"] = $output;
		$ret = json_decode($output,true);
		
		$product_title = isset($ret["product_title"]) ? $ret["product_title"] : '';		
		
		return ($product_title) ? true : false;
	}
}
